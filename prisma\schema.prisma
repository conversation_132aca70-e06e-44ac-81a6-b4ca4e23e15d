generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id              String            @id @default(uuid())
  email           String            @unique
  name            String?
  emailVerified   Boolean
  image           String?
  createdAt       DateTime
  updatedAt       DateTime
  sessions        Session[]
  accounts        Account[]
  Subscription    Subscription[]
  OneTimePurchase OneTimePurchase[]

  @@map("user")
}

model Subscription {
  id                 String   @id
  userId             String
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product            String
  providerCustomerId String
  status             String
  created_at         DateTime @default(now())
  updated_at         DateTime @default(now())

  @@map("subscription")
}

model OneTimePurchase {
  id                 String   @id
  userId             String
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  product            String
  providerCustomerId String
  created_at         DateTime @default(now())
  updated_at         DateTime @default(now())

  @@map("onetimepurchase")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}
