/**
 * Custom Supabase Adapter for Better Auth
 * 
 * This adapter integrates Better Auth with Supabase by providing
 * a bridge between Better Auth's database operations and Supabase's
 * PostgreSQL database with proper type safety and error handling.
 */

import { createAdapter, type AdapterDebugLogs } from "better-auth/adapters";
import { supabaseAdmin } from '@/lib/supabase';
import { Database } from '@/types/database';

interface SupabaseAdapterConfig {
  /**
   * Helps you debug issues with the adapter.
   */
  debugLogs?: AdapterDebugLogs;
  /**
   * If the table names in the schema are plural.
   */
  usePlural?: boolean;
}

/**
 * Map Better Auth model names to Supabase table names
 */
const getTableName = (model: string, usePlural: boolean = false): string => {
  const tableMap: Record<string, string> = {
    user: usePlural ? 'users' : 'users',
    session: usePlural ? 'sessions' : 'sessions',
    account: usePlural ? 'accounts' : 'accounts',
    verification: usePlural ? 'verifications' : 'verifications',
    subscription: usePlural ? 'subscriptions' : 'subscriptions',
    oneTimePurchase: usePlural ? 'one_time_purchases' : 'one_time_purchases',
  };
  
  return tableMap[model] || model;
};

/**
 * Transform Better Auth field names to Supabase column names
 */
const transformFieldNames = (data: Record<string, any>): Record<string, any> => {
  const fieldMap: Record<string, string> = {
    emailVerified: 'email_verified',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    userId: 'user_id',
    expiresAt: 'expires_at',
    ipAddress: 'ip_address',
    userAgent: 'user_agent',
    accountId: 'account_id',
    providerId: 'provider_id',
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
    idToken: 'id_token',
    accessTokenExpiresAt: 'access_token_expires_at',
    refreshTokenExpiresAt: 'refresh_token_expires_at',
    providerCustomerId: 'provider_customer_id',
  };

  const transformed: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    const mappedKey = fieldMap[key] || key;
    transformed[mappedKey] = value;
  }
  
  return transformed;
};

/**
 * Transform Supabase column names back to Better Auth field names
 */
const transformFromSupabase = (data: Record<string, any>): Record<string, any> => {
  const reverseFieldMap: Record<string, string> = {
    email_verified: 'emailVerified',
    created_at: 'createdAt',
    updated_at: 'updatedAt',
    user_id: 'userId',
    expires_at: 'expiresAt',
    ip_address: 'ipAddress',
    user_agent: 'userAgent',
    account_id: 'accountId',
    provider_id: 'providerId',
    access_token: 'accessToken',
    refresh_token: 'refreshToken',
    id_token: 'idToken',
    access_token_expires_at: 'accessTokenExpiresAt',
    refresh_token_expires_at: 'refreshTokenExpiresAt',
    provider_customer_id: 'providerCustomerId',
  };

  const transformed: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    const mappedKey = reverseFieldMap[key] || key;
    transformed[mappedKey] = value;
  }
  
  return transformed;
};

/**
 * Build WHERE clause for Supabase queries
 */
const buildWhereClause = (query: any, where: Record<string, any>) => {
  let currentQuery = query;
  
  for (const [key, value] of Object.entries(where)) {
    const transformedKey = transformFieldNames({ [key]: value });
    const columnName = Object.keys(transformedKey)[0];
    
    if (value === null) {
      currentQuery = currentQuery.is(columnName, null);
    } else if (Array.isArray(value)) {
      currentQuery = currentQuery.in(columnName, value);
    } else {
      currentQuery = currentQuery.eq(columnName, value);
    }
  }
  
  return currentQuery;
};

export const supabaseAdapter = (config: SupabaseAdapterConfig = {}) =>
  createAdapter({
    config: {
      adapterId: "supabase-adapter",
      adapterName: "Supabase Adapter",
      usePlural: config.usePlural ?? false,
      debugLogs: config.debugLogs ?? false,
      supportsJSON: true,
      supportsDates: true,
      supportsBooleans: true,
      supportsNumericIds: false, // Supabase uses UUIDs by default
    },
    adapter: ({ options, debugLog }) => {
      return {
        async create({ data, model, select }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            const transformedData = transformFieldNames(data);
            
            debugLog?.create && console.log(`Creating in ${tableName}:`, transformedData);
            
            let query = supabaseAdmin.from(tableName).insert(transformedData);
            
            if (select && select.length > 0) {
              const transformedSelect = select.map(field => 
                transformFieldNames({ [field]: true })[Object.keys(transformFieldNames({ [field]: true }))[0]]
              );
              query = query.select(transformedSelect.join(','));
            } else {
              query = query.select('*');
            }
            
            const { data: result, error } = await query.single();
            
            if (error) {
              throw new Error(`Create failed: ${error.message}`);
            }
            
            return transformFromSupabase(result);
          } catch (error) {
            debugLog?.create && console.error(`Create error in ${model}:`, error);
            throw error;
          }
        },

        async findOne({ model, where, select }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            
            debugLog?.findOne && console.log(`Finding one in ${tableName}:`, where);
            
            let query = supabaseAdmin.from(tableName).select('*');
            query = buildWhereClause(query, where);
            
            const { data, error } = await query.single();
            
            if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
              throw new Error(`FindOne failed: ${error.message}`);
            }
            
            return data ? transformFromSupabase(data) : null;
          } catch (error) {
            debugLog?.findOne && console.error(`FindOne error in ${model}:`, error);
            if (error instanceof Error && error.message.includes('PGRST116')) {
              return null;
            }
            throw error;
          }
        },

        async findMany({ model, where, limit, offset, sortBy }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            
            debugLog?.findMany && console.log(`Finding many in ${tableName}:`, { where, limit, offset, sortBy });
            
            let query = supabaseAdmin.from(tableName).select('*');
            
            if (where) {
              query = buildWhereClause(query, where);
            }
            
            if (limit) {
              query = query.limit(limit);
            }
            
            if (offset) {
              query = query.range(offset, offset + (limit || 1000) - 1);
            }
            
            if (sortBy) {
              for (const sort of sortBy) {
                const transformedField = transformFieldNames({ [sort.field]: true });
                const columnName = Object.keys(transformedField)[0];
                query = query.order(columnName, { ascending: sort.direction === 'asc' });
              }
            }
            
            const { data, error } = await query;
            
            if (error) {
              throw new Error(`FindMany failed: ${error.message}`);
            }
            
            return data ? data.map(transformFromSupabase) : [];
          } catch (error) {
            debugLog?.findMany && console.error(`FindMany error in ${model}:`, error);
            throw error;
          }
        },

        async update({ model, where, update }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            const transformedUpdate = transformFieldNames(update);
            
            debugLog?.update && console.log(`Updating in ${tableName}:`, { where, update: transformedUpdate });
            
            let query = supabaseAdmin.from(tableName).update(transformedUpdate);
            query = buildWhereClause(query, where);
            query = query.select('*');
            
            const { data, error } = await query.single();
            
            if (error) {
              throw new Error(`Update failed: ${error.message}`);
            }
            
            return transformFromSupabase(data);
          } catch (error) {
            debugLog?.update && console.error(`Update error in ${model}:`, error);
            throw error;
          }
        },

        async updateMany({ model, where, update }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            const transformedUpdate = transformFieldNames(update);
            
            debugLog?.updateMany && console.log(`Updating many in ${tableName}:`, { where, update: transformedUpdate });
            
            let query = supabaseAdmin.from(tableName).update(transformedUpdate);
            query = buildWhereClause(query, where);
            
            const { count, error } = await query;
            
            if (error) {
              throw new Error(`UpdateMany failed: ${error.message}`);
            }
            
            return count || 0;
          } catch (error) {
            debugLog?.updateMany && console.error(`UpdateMany error in ${model}:`, error);
            throw error;
          }
        },

        async delete({ model, where }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            
            debugLog?.delete && console.log(`Deleting from ${tableName}:`, where);
            
            let query = supabaseAdmin.from(tableName).delete();
            query = buildWhereClause(query, where);
            
            const { error } = await query;
            
            if (error) {
              throw new Error(`Delete failed: ${error.message}`);
            }
          } catch (error) {
            debugLog?.delete && console.error(`Delete error in ${model}:`, error);
            throw error;
          }
        },

        async deleteMany({ model, where }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            
            debugLog?.deleteMany && console.log(`Deleting many from ${tableName}:`, where);
            
            let query = supabaseAdmin.from(tableName).delete();
            query = buildWhereClause(query, where);
            
            const { count, error } = await query;
            
            if (error) {
              throw new Error(`DeleteMany failed: ${error.message}`);
            }
            
            return count || 0;
          } catch (error) {
            debugLog?.deleteMany && console.error(`DeleteMany error in ${model}:`, error);
            throw error;
          }
        },

        async count({ model, where }) {
          try {
            const tableName = getTableName(model, options.usePlural);
            
            debugLog?.count && console.log(`Counting in ${tableName}:`, where);
            
            let query = supabaseAdmin.from(tableName).select('*', { count: 'exact', head: true });
            
            if (where) {
              query = buildWhereClause(query, where);
            }
            
            const { count, error } = await query;
            
            if (error) {
              throw new Error(`Count failed: ${error.message}`);
            }
            
            return count || 0;
          } catch (error) {
            debugLog?.count && console.error(`Count error in ${model}:`, error);
            throw error;
          }
        },
      };
    },
  });
