# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL="https://your-project.supabase.co" # Replace with your Supabase project URL
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key" # Replace with your Supabase anon key
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key" # Replace with your Supabase service role key (keep secret!)

# Database Configuration (for Better Auth)
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres" # Replace with your Supabase database URL

# Creem Payment Configuration
CREEM_API_KEY="creem_test_my-api-key" # Replace with your Creem API key from test mode

# Better Auth Configuration
BETTER_AUTH_SECRET="supersecretstring" # Use any random string as your secret (generate a secure one!)
BETTER_AUTH_URL=http://localhost:3000 # Base URL of your app

# OAuth Configuration (optional)
GITHUB_CLIENT_ID="your-github-client-id" # Replace with your GitHub OAuth app client ID
GITHUB_CLIENT_SECRET="your-github-client-secret" # Replace with your GitHub OAuth app client secret

# Application URLs
SUCCESS_URL=http://localhost:3000/account # Redirect URL after successful purchase
