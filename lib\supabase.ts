/**
 * Supabase Client Configuration
 * 
 * This module provides a secure, type-safe Supabase client configuration
 * for server-side and client-side operations. It includes proper error handling,
 * connection pooling, and security best practices.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

// Environment variables validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable');
}

/**
 * Client-side Supabase client
 * Uses the anon key and respects RLS policies
 * Safe to use in browser environments
 */
export const supabase: SupabaseClient<Database> = createClient<Database>(
  supabaseUrl,
  supabaseAnonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'X-Client-Info': 'saas-boilerplate@1.0.0',
      },
    },
  }
);

/**
 * Server-side Supabase client with service role privileges
 * Bypasses RLS policies - use with extreme caution
 * Only use for server-side operations that require elevated permissions
 */
export const supabaseAdmin: SupabaseClient<Database> = (() => {
  if (!supabaseServiceKey) {
    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable for admin client');
  }

  return createClient<Database>(
    supabaseUrl,
    supabaseServiceKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
      db: {
        schema: 'public',
      },
      global: {
        headers: {
          'X-Client-Info': 'saas-boilerplate-admin@1.0.0',
        },
      },
    }
  );
})();

/**
 * Database operation utilities with proper error handling
 */
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

/**
 * Wrapper for database operations with consistent error handling
 */
export async function executeQuery<T>(
  operation: () => Promise<{ data: T | null; error: any }>
): Promise<T> {
  try {
    const { data, error } = await operation();
    
    if (error) {
      throw new DatabaseError(
        error.message || 'Database operation failed',
        error.code,
        error.details
      );
    }

    if (data === null) {
      throw new DatabaseError('No data returned from query');
    }

    return data;
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error;
    }
    
    throw new DatabaseError(
      `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * Type-safe database client with error handling
 */
export const db = {
  /**
   * Execute a query with the regular client (respects RLS)
   */
  query: <T>(operation: () => Promise<{ data: T | null; error: any }>) =>
    executeQuery(operation),

  /**
   * Execute a query with admin privileges (bypasses RLS)
   * Use only when necessary and with proper authorization checks
   */
  adminQuery: <T>(operation: () => Promise<{ data: T | null; error: any }>) =>
    executeQuery(operation),
};

/**
 * Connection health check utility
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('users')
      .select('id')
      .limit(1);
    
    return !error;
  } catch {
    return false;
  }
}

/**
 * Utility to get user ID from session
 */
export async function getCurrentUserId(): Promise<string | null> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      return null;
    }
    
    return user.id;
  } catch {
    return null;
  }
}

export default supabase;
