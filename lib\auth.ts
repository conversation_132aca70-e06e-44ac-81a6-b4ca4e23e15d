import { betterAuth } from "better-auth";
import { supabaseAdapter } from "@/lib/better-auth-supabase-adapter";

export const auth = betterAuth({
  database: supabaseAdapter({
    usePlural: false,
    debugLogs: process.env.NODE_ENV === 'development',
  }),
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
  },
});
