import { betterAuth } from "better-auth";

// For now, we'll use a simple configuration that works with Supabase
// The database operations will be handled through our custom database layer
export const auth = betterAuth({
  database: process.env.DATABASE_URL ? {
    // Use a connection string format that Better Au<PERSON> can understand
    // This will be replaced with proper Supabase integration once the adapter is stable
    connectionString: process.env.DATABASE_URL,
    type: "postgres",
  } : undefined,
  emailAndPassword: {
    enabled: true,
  },
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID as string,
      clientSecret: process.env.GITHUB_CLIENT_SECRET as string,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day
  },
  advanced: {
    crossSubDomainCookies: {
      enabled: false,
    },
  },
});
