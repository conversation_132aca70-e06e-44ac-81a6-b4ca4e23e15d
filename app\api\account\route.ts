/**
 * Account API Route
 *
 * This route provides access to a user's purchase history, including both subscriptions
 * and one-time purchases. It requires authentication and uses Supabase for
 * efficient and secure data retrieval.
 *
 * @route GET /api/account
 */

import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { subscriptionOperations, oneTimePurchaseOperations } from "@/lib/database";
import { DatabaseError } from "@/lib/supabase";

/**
 * Response interface for user purchases endpoint
 * Contains arrays of both subscription and one-time purchase records
 */
export interface UserPurchasesResponse {
  subscriptions: {
    id: string; // Unique identifier for the subscription
    product: string; // Product identifier or name
    providerCustomerId: string; // Customer ID from the payment provider
    status: string; // Current subscription status
    created_at: Date; // Subscription creation timestamp
    updated_at: Date; // Last update timestamp
  }[];
  oneTimePurchases: {
    id: string; // Unique identifier for the purchase
    product: string; // Product identifier or name
    providerCustomerId: string; // Customer ID from the payment provider
    created_at: Date; // Purchase timestamp
    updated_at: Date; // Last update timestamp
  }[];
}

/**
 * GET handler for retrieving user's purchase history
 *
 * @returns {Promise<NextResponse>} JSON response containing:
 *   - On success: UserPurchasesResponse with subscriptions and one-time purchases
 *   - On error: 401 Unauthorized if no valid session
 *
 * Example successful response:
 * {
 *   "subscriptions": [{
 *     "id": "sub_123",
 *     "product": "premium_plan",
 *     "providerCustomerId": "cus_456",
 *     "status": "active",
 *     "created_at": "2024-01-01T00:00:00Z",
 *     "updated_at": "2024-01-01T00:00:00Z"
 *   }],
 *   "oneTimePurchases": [{
 *     "id": "pur_789",
 *     "product": "single_item",
 *     "providerCustomerId": "cus_456",
 *     "created_at": "2024-01-01T00:00:00Z",
 *     "updated_at": "2024-01-01T00:00:00Z"
 *   }]
 * }
 */
export async function GET() {
  try {
    const session = await auth.api.getSession({ headers: headers() });

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Fetch both subscriptions and one-time purchases concurrently using Supabase
    // This approach provides better performance and type safety compared to raw SQL
    // while maintaining the same functionality as the original Prisma implementation
    const [subscriptions, oneTimePurchases] = await Promise.all([
      subscriptionOperations.findByUserId(session.user.id),
      oneTimePurchaseOperations.findByUserId(session.user.id),
    ]);

    // Transform the data to match the expected response format
    const transformedSubscriptions = subscriptions.map(sub => ({
      id: sub.id,
      product: sub.product,
      providerCustomerId: sub.provider_customer_id,
      status: sub.status,
      created_at: new Date(sub.created_at),
      updated_at: new Date(sub.updated_at),
    }));

    const transformedOneTimePurchases = oneTimePurchases.map(purchase => ({
      id: purchase.id,
      product: purchase.product,
      providerCustomerId: purchase.provider_customer_id,
      created_at: new Date(purchase.created_at),
      updated_at: new Date(purchase.updated_at),
    }));

    return NextResponse.json({
      subscriptions: transformedSubscriptions,
      oneTimePurchases: transformedOneTimePurchases,
    } as UserPurchasesResponse);
  } catch (error) {
    console.error('Account API error:', error);

    if (error instanceof DatabaseError) {
      return NextResponse.json(
        { error: "Database error occurred" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
